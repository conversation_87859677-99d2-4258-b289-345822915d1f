<div align="center">

# 🛡️ AntiCheat

**Advanced Anti-Cheat Detection System for Rust Servers**

[![Version](https://img.shields.io/badge/version-0.1b-blue.svg)](https://github.com/golgolak/anticheat)
[![License](https://img.shields.io/badge/license-Proprietary-red.svg)](./EULA.md)
[![Rust](https://img.shields.io/badge/game-Rust-orange.svg)](https://rust.facepunch.com/)
[![uMod](https://img.shields.io/badge/framework-uMod%2FOxide-green.svg)](https://umod.org/)

*A sophisticated, stripped-down fork of Arkan focusing on precise cheat detection*

---

</div>

## 📋 Table of Contents

- [🎯 Overview](#-overview)
- [✨ Features](#-features)
- [🔧 Installation](#-installation)
- [⚙️ Configuration](#️-configuration)
- [🎮 Commands](#-commands)
- [🔑 Permissions](#-permissions)
- [🚨 Detection Methods](#-detection-methods)
- [📊 Discord Integration](#-discord-integration)
- [🛠️ API](#️-api)
- [📈 Performance](#-performance)
- [🤝 Support](#-support)
- [📄 License](#-license)

## 🎯 Overview

AntiCheat is a cutting-edge anti-cheat detection system designed specifically for Rust servers. This plugin represents a refined fork of the original Arkan system, focusing exclusively on accurate cheat detection without unnecessary bloat.

### 🎪 Key Highlights

- **🎯 Precision Detection**: Advanced mathematical algorithms for accurate cheat identification
- **⚡ Performance Optimized**: Minimal server impact with efficient processing
- **🔄 Real-time Monitoring**: Continuous player behavior analysis
- **📱 Discord Integration**: Instant notifications with detailed violation reports
- **🛡️ PvP Focused**: Specifically designed for player vs player interactions
- **🔧 Highly Configurable**: Extensive customization options for different server needs

## ✨ Features

### 🎯 Detection Systems

| Detection Type | Description | Status |
|----------------|-------------|---------|
| **No Recoil (NR)** | Detects recoil compensation scripts and macros | ✅ Active |
| **Aimbot (AIM)** | Analyzes projectile trajectories for impossible shots | ✅ Active |
| **In Rock (IR)** | Identifies shots through terrain and solid objects | ✅ Active |

### 🔧 Core Features

- **🎮 Player vs Player Focus**: Only monitors PvP interactions, ignoring NPCs and animals
- **📊 Statistical Analysis**: Advanced probability calculations for violation detection
- **🔄 Real-time Processing**: Immediate analysis of player actions
- **💾 Data Persistence**: Automatic saving and loading of violation data
- **🎛️ Weapon-Specific Settings**: Individual configuration for each weapon type
- **🔐 Permission System**: Flexible whitelist/blacklist functionality
- **📈 Violation Tracking**: Comprehensive logging and reporting system

### 🌐 Integration Support

- **Discord API**: Native webhook support for instant notifications
- **DiscordMessages Plugin**: Alternative Discord integration method
- **uMod/Oxide**: Full compatibility with the Oxide framework
- **Custom APIs**: Extensible architecture for third-party integrations

## 🔧 Installation

### Prerequisites

- **Rust Server**: Dedicated Rust server with admin access
- **uMod/Oxide**: Latest version of Oxide framework installed
- **Dependencies** (Optional):
  - `DiscordApi` plugin for Discord integration
  - `DiscordMessages` plugin as alternative Discord method

### Installation Steps

1. **Download**: Obtain the `AntiCheat.cs` file
2. **Upload**: Place the file in your server's `oxide/plugins/` directory
3. **Load**: The plugin will automatically load and create default configuration
4. **Configure**: Edit the configuration file to match your server needs
5. **Permissions**: Set up appropriate permissions for administrators

```bash
# Server console commands
oxide.load AntiCheat
oxide.grant group admin anticheat.use
```

### File Structure

```
oxide/
├── plugins/
│   └── AntiCheat.cs
├── config/
│   └── AntiCheat.json
├── data/
│   └── AntiCheat/
│       └── [server_timestamp].json
└── lang/
    └── en/
        └── AntiCheat.json
```

## ⚙️ Configuration

The plugin creates a comprehensive configuration file with weapon-specific settings and detection parameters.

### 🎛️ Main Configuration Options

```json
{
  "NRProcessTimer": 4.0,
  "EPSILON": 0.005,
  "projectileTrajectoryForgiveness": 0.3,
  "hitDistanceForgiveness": 0.25,
  "minDistanceAIMCheck": 10.0,
  "inRockCheckDistance": 200.0,
  "isDetectAIM": true,
  "isDetectNR": true,
  "isDetectIR": true,
  "debugMessages": true,
  "autoSave": true,
  "NRViolationAngle": 0.3,
  "NRViolationScreenDistance": 5.0
}
```

### 🔫 Weapon-Specific Configuration

Each weapon can be individually configured with specific detection parameters:

```json
{
  "weaponsConfig": {
    "rifle.ak": {
      "NRDetectEnabled": true,
      "AIMDetectEnabled": true,
      "weaponMinTimeShotsInterval": 0.09375,
      "weaponMaxTimeShotsInterval": 0.15625,
      "NRMinShotsCountToCheck": 5,
      "NRViolationProbability": 70.0
    }
  }
}
```

### 🎯 Detection Parameters

| Parameter | Description | Default | Range |
|-----------|-------------|---------|-------|
| `NRProcessTimer` | Delay before processing shots for NR detection | 4.0s | 1.0-10.0 |
| `EPSILON` | Mathematical precision for calculations | 0.005 | 0.001-0.01 |
| `projectileTrajectoryForgiveness` | Tolerance for trajectory analysis | 0.3 | 0.1-1.0 |
| `NRViolationProbability` | Threshold for NR violation detection | 70% | 50-95 |

## 🎮 Commands

### 👨‍💼 Admin Commands

| Command | Description | Permission Required |
|---------|-------------|-------------------|
| `/AntiCheat` | Display violation logs and statistics | `anticheat.use` |
| `/AntiCheatinfo` | Show plugin information and version | `anticheat.use` |
| `/AntiCheatnr [player]` | View No Recoil violations for specific player | `anticheat.use` |
| `/AntiCheataim [player]` | View Aimbot violations for specific player | `anticheat.use` |
| `/AntiCheatsave [filename]` | Manually save violation data | `anticheat.use` |
| `/AntiCheatload [filename]` | Load violation data from file | `anticheat.use` |
| `/AntiCheatclear` | Clear all violation data | `anticheat.use` |

### 🖥️ Console Commands

```bash
# Console command for server administrators
AntiCheat [player_name_or_id]
```

### 📊 Command Examples

```bash
# View all violations
/AntiCheat

# Check specific player's No Recoil violations
/AntiCheatnr "PlayerName"

# Save current violation data with custom filename
/AntiCheatsave "backup_2025_01_15"

# Load specific violation data file
/AntiCheatload "backup_2025_01_15"
```

## 🔑 Permissions

The plugin uses a comprehensive permission system to control access and functionality.

### 🛡️ Permission Nodes

| Permission | Description | Default Group |
|------------|-------------|---------------|
| `anticheat.use` | Access to all AntiCheat commands and features | Admin |
| `anticheat.nr.whitelist` | Exempt from No Recoil detection | None |
| `anticheat.aim.whitelist` | Exempt from Aimbot detection | None |
| `anticheat.ir.whitelist` | Exempt from In Rock detection | None |
| `anticheat.nr.blacklist` | Force No Recoil detection (when blacklist mode enabled) | None |
| `anticheat.aim.blacklist` | Force Aimbot detection (when blacklist mode enabled) | None |
| `anticheat.ir.blacklist` | Force In Rock detection (when blacklist mode enabled) | None |

### 🔧 Permission Setup

```bash
# Grant admin access to AntiCheat
oxide.grant group admin anticheat.use

# Whitelist specific players from detection
oxide.grant user "PlayerName" anticheat.nr.whitelist
oxide.grant user "PlayerName" anticheat.aim.whitelist

# Grant permissions by SteamID
oxide.grant user 76561198000000000 anticheat.use
```

### ⚙️ Blacklist vs Whitelist Mode

The plugin supports two permission modes:

- **Whitelist Mode** (Default): Only players WITHOUT whitelist permissions are monitored
- **Blacklist Mode**: Only players WITH blacklist permissions are monitored

Configure in the config file:
```json
{
  "checkBlacklist": false  // false = whitelist mode, true = blacklist mode
}
```

## 🚨 Detection Methods

### 🎯 No Recoil Detection (NR)

Advanced mathematical analysis of weapon recoil patterns to detect compensation scripts.

**How it works:**
- Analyzes shot-to-shot angle variations
- Calculates recoil screen distance
- Uses statistical probability models
- Considers weapon-specific recoil patterns

**Detection Criteria:**
- Minimum shots required: 5 (configurable per weapon)
- Violation angle threshold: 0.3 degrees
- Screen distance threshold: 5 pixels
- Probability threshold: 70%

### 🎯 Aimbot Detection (AIM)

Sophisticated projectile trajectory analysis to identify impossible shots.

**How it works:**
- Simulates realistic projectile physics
- Compares actual vs expected trajectories
- Accounts for gravity, drag, and ballistics
- Validates hit positions against trajectory paths

**Detection Features:**
- Real-time trajectory calculation
- Physics-based validation
- Distance-based forgiveness factors
- Projectile-specific analysis (bullets, arrows, etc.)

### 🎯 In Rock Detection (IR)

Detects shots that pass through solid terrain or objects.

**How it works:**
- Raycasting from shooter to target
- Collision detection with terrain layers
- Analysis of shot origin and impact points
- Validation against world geometry

**Detection Parameters:**
- Maximum check distance: 200 meters
- Terrain layer validation
- Rock/obstacle collision detection
- Line-of-sight verification

## 📊 Discord Integration

### 🔗 Webhook Configuration

The plugin supports multiple Discord integration methods:

```json
{
  "discordWebhookUrl": "https://discord.com/api/webhooks/...",
  "useDiscordApi": true,
  "useDiscordMessages": false
}
```

### 📱 Notification Features

- **Real-time Alerts**: Instant violation notifications
- **Detailed Reports**: Comprehensive violation information
- **Server Links**: Direct links to connect to your server
- **Embed Formatting**: Rich, formatted Discord messages
- **Violation Statistics**: Player violation history and trends

### 📋 Discord Message Format

```json
{
  "embeds": [{
    "title": "🚨 AntiCheat Violation Detected",
    "color": 15158332,
    "fields": [
      {
        "name": "Player",
        "value": "PlayerName (76561198000000000)",
        "inline": true
      },
      {
        "name": "Violation Type",
        "value": "No Recoil",
        "inline": true
      },
      {
        "name": "Probability",
        "value": "85.7%",
        "inline": true
      },
      {
        "name": "Server",
        "value": "[Connect](steam://connect/your.server.ip:port)",
        "inline": false
      }
    ],
    "timestamp": "2025-01-15T10:30:00.000Z"
  }]
}
```

## 🛠️ API

### 🔌 Plugin Hooks

The AntiCheat plugin provides several hooks for integration with other plugins:

```csharp
// Called when a violation is detected
void OnAntiCheatViolation(BasePlayer player, string violationType, float probability)
{
    // Your custom handling code here
}

// Called when violation data is saved
void OnAntiCheatDataSaved(string filename)
{
    // Your custom handling code here
}
```

### 📊 Data Access

Access violation data programmatically:

```csharp
// Get player violation count
var violations = AntiCheat?.Call("GetPlayerViolations", player.userID);

// Check if player has specific violation type
var hasNRViolations = AntiCheat?.Call("HasViolationType", player.userID, "NR");
```

## 📈 Performance

### ⚡ Optimization Features

- **Efficient Processing**: Minimal CPU impact during gameplay
- **Smart Caching**: Optimized data structures for fast access
- **Selective Monitoring**: Only processes relevant player interactions
- **Garbage Collection**: Automatic cleanup of expired data
- **Configurable Timers**: Adjustable processing intervals

### 📊 Performance Metrics

| Metric | Value | Impact |
|--------|-------|--------|
| **CPU Usage** | < 1% | Minimal |
| **Memory Usage** | ~10-50MB | Low |
| **Network Impact** | None | Zero |
| **Storage** | ~1-5MB/day | Minimal |

### 🔧 Performance Tuning

```json
{
  "NRProcessTimer": 4.0,        // Increase for better performance
  "autoSave": true,             // Disable for slight performance gain
  "debugMessages": false,       // Disable in production
  "physicsSteps": 100          // Reduce for better performance
}
```

## 🔍 Troubleshooting

### ❓ Common Issues

**Q: Plugin not detecting violations**
- Verify permissions are set correctly
- Check if detection is enabled in config
- Ensure players are not whitelisted
- Verify weapon configurations

**Q: Discord notifications not working**
- Check webhook URL is valid
- Verify Discord plugin dependencies
- Test webhook manually
- Check server console for errors

**Q: High false positive rate**
- Adjust violation probability thresholds
- Increase forgiveness parameters
- Review weapon-specific settings
- Consider network latency factors

### 🐛 Debug Mode

Enable debug mode for detailed logging:

```json
{
  "debugMessages": true
}
```

This will output detailed information to the server console for troubleshooting.

### 📝 Log Files

Check these locations for diagnostic information:
- Server console output
- `oxide/logs/` directory
- `oxide/data/AntiCheat/` violation data

## 🤝 Support

### 📞 Getting Help

- **Documentation**: This README and inline code comments
- **Issues**: Report bugs and feature requests via GitHub issues
- **Community**: Join the Rust modding community discussions

### 🔄 Updates

- **Version**: Current version is 0.1b
- **Compatibility**: Tested with latest Rust and Oxide versions
- **Changelog**: Check commit history for detailed changes

### 🛡️ Security

- **Reporting**: Report security vulnerabilities privately
- **Updates**: Keep plugin updated for latest security patches
- **Configuration**: Review permissions and settings regularly

## 📄 License

This software is proprietary and licensed under a custom EULA. See [EULA.md](./EULA.md) for full terms.

**Key Points:**
- ✅ Personal and business use permitted
- ❌ Distribution and modification prohibited
- ❌ Reverse engineering not allowed
- ⚖️ Legal action for unauthorized use

---

<div align="center">

**Made with ❤️ by [Golgolak](https://github.com/golgolak)**

*Keeping Rust servers fair and competitive*

[![GitHub](https://img.shields.io/badge/GitHub-golgolak-black.svg?logo=github)](https://github.com/golgolak)
[![Rust](https://img.shields.io/badge/Rust-Server%20Plugin-orange.svg)](https://rust.facepunch.com/)

</div>
